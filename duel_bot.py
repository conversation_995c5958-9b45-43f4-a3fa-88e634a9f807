import telebot
import time
import re
from telebot import types
from config import BOT_TOKEN, MESSAGES, DUEL_TIMEOUT
from duel_game import DuelGame
from database import DuelDatabase

# Инициализация бота
bot = telebot.TeleBot(BOT_TOKEN)
duel_game = DuelGame()
db = DuelDatabase()

# Словарь для хранения таймеров
timers = {}

def get_user_display_name(user):
    """Получить отображаемое имя пользователя"""
    if user.username:
        return f"@{user.username}"
    elif user.first_name:
        return user.first_name
    else:
        return f"User{user.id}"

def extract_user_id_from_mention(text):
    """Извлечь ID пользователя из упоминания"""
    # Ищем @username в тексте
    mention_match = re.search(r'@(\w+)', text)
    if mention_match:
        return mention_match.group(1)
    return None

@bot.message_handler(commands=['start', 'help'])
def send_help(message):
    """Отправить справку"""
    bot.reply_to(message, MESSAGES['help_text'])

@bot.message_handler(commands=['duel'])
def challenge_duel(message):
    """Вызвать на дуэль"""
    if message.chat.type == 'private':
        bot.reply_to(message, "❌ Дуэли доступны только в группах!")
        return
    
    # Добавляем пользователя в базу данных
    challenger = message.from_user
    db.add_user(challenger.id, challenger.username, challenger.first_name, challenger.last_name)
    
    # Проверяем, есть ли активная дуэль
    if duel_game.get_duel(message.chat.id):
        bot.reply_to(message, "❌ В этой группе уже идет дуэль!")
        return
    
    # Ищем упоминание пользователя
    if message.reply_to_message:
        # Если это ответ на сообщение
        opponent = message.reply_to_message.from_user
        if opponent.id == challenger.id:
            bot.reply_to(message, "❌ Нельзя вызвать самого себя на дуэль!")
            return
        if opponent.is_bot:
            bot.reply_to(message, "❌ Нельзя вызвать бота на дуэль!")
            return
    else:
        # Ищем @username в тексте команды
        username = extract_user_id_from_mention(message.text)
        if not username:
            bot.reply_to(message, "❌ Укажите противника: /duel @username или ответьте на сообщение")
            return
        
        # Здесь нужно найти пользователя по username
        # Для упрощения попросим использовать reply
        bot.reply_to(message, "❌ Ответьте на сообщение пользователя, которого хотите вызвать на дуэль")
        return
    
    # Добавляем противника в базу данных
    db.add_user(opponent.id, opponent.username, opponent.first_name, opponent.last_name)
    
    # Создаем дуэль
    challenger_name = get_user_display_name(challenger)
    opponent_name = get_user_display_name(opponent)
    
    duel = duel_game.create_duel(
        message.chat.id, 
        challenger.id, challenger_name,
        opponent.id, opponent_name
    )
    
    # Создаем клавиатуру для ответа
    markup = types.InlineKeyboardMarkup()
    accept_btn = types.InlineKeyboardButton("✅ Принять", callback_data=f"accept_duel_{opponent.id}")
    decline_btn = types.InlineKeyboardButton("❌ Отклонить", callback_data=f"decline_duel_{opponent.id}")
    markup.add(accept_btn, decline_btn)
    
    challenge_text = f"⚔️ {challenger_name} вызывает {opponent_name} на дуэль!\n\n"
    challenge_text += f"🎯 Тип дуэли: {duel['type']}\n"
    challenge_text += f"🔥 Раундов: {duel['max_rounds']}\n\n"
    challenge_text += f"{opponent_name}, принимаете вызов?"
    
    bot.send_message(message.chat.id, challenge_text, reply_markup=markup)
    
    # Устанавливаем таймер на автоматическое отклонение
    def auto_decline():
        time.sleep(60)  # 60 секунд на принятие решения
        current_duel = duel_game.get_duel(message.chat.id)
        if current_duel and current_duel['status'] == 'pending':
            duel_game.decline_duel(message.chat.id)
            bot.send_message(message.chat.id, f"⏰ {opponent_name} не ответил на вызов. Дуэль отменена.")
    
    import threading
    timer = threading.Timer(60.0, auto_decline)
    timer.start()
    timers[message.chat.id] = timer

@bot.callback_query_handler(func=lambda call: call.data.startswith(('accept_duel_', 'decline_duel_')))
def handle_duel_response(call):
    """Обработать ответ на вызов дуэли"""
    action, user_id = call.data.rsplit('_', 1)
    user_id = int(user_id)
    
    # Проверяем, что отвечает правильный пользователь
    if call.from_user.id != user_id:
        bot.answer_callback_query(call.id, "❌ Это не ваш вызов!")
        return
    
    duel = duel_game.get_duel(call.message.chat.id)
    if not duel or duel['status'] != 'pending':
        bot.answer_callback_query(call.id, "❌ Дуэль уже завершена!")
        return
    
    # Отменяем таймер
    if call.message.chat.id in timers:
        timers[call.message.chat.id].cancel()
        del timers[call.message.chat.id]
    
    if action == 'accept_duel':
        duel_game.accept_duel(call.message.chat.id)
        bot.edit_message_text(
            "✅ Дуэль принята! Начинаем бой!\n\n🎯 Приготовьтесь к первому раунду...",
            call.message.chat.id,
            call.message.message_id
        )
        
        # Запускаем первый раунд через 3 секунды
        def start_first_round():
            time.sleep(3)
            start_round(call.message.chat.id)
        
        import threading
        threading.Thread(target=start_first_round).start()
        
    else:  # decline_duel
        duel_game.decline_duel(call.message.chat.id)
        opponent_name = duel['opponent']['name']
        bot.edit_message_text(
            f"❌ {opponent_name} отклонил дуэль.",
            call.message.chat.id,
            call.message.message_id
        )
    
    bot.answer_callback_query(call.id)

def start_round(chat_id):
    """Начать новый раунд"""
    duel = duel_game.start_round(chat_id)
    if not duel:
        return
    
    question = duel['current_question']
    round_num = duel['round']
    max_rounds = duel['max_rounds']
    
    round_text = f"🔥 Раунд {round_num}/{max_rounds}\n\n"
    round_text += f"❓ {question['text']}\n\n"
    
    if question['type'] == 'reaction':
        round_text += "⚡ Первый правильный ответ побеждает!"
    else:
        round_text += f"⏰ У вас есть {DUEL_TIMEOUT} секунд на ответ!"
    
    bot.send_message(chat_id, round_text)
    
    # Устанавливаем таймер на завершение раунда
    def round_timeout():
        time.sleep(DUEL_TIMEOUT)
        current_duel = duel_game.get_duel(chat_id)
        if current_duel and current_duel['status'] == 'active' and len(current_duel['answers']) < 2:
            # Завершаем раунд принудительно
            result = duel_game.evaluate_round(chat_id)
            handle_round_result(chat_id, result)
    
    import threading
    timer = threading.Timer(DUEL_TIMEOUT, round_timeout)
    timer.start()

@bot.message_handler(func=lambda message: True)
def handle_duel_answers(message):
    """Обработать ответы в дуэли"""
    if message.chat.type == 'private':
        return
    
    duel = duel_game.get_duel(message.chat.id)
    if not duel or duel['status'] != 'active':
        return
    
    # Проверяем, участвует ли пользователь в дуэли
    if message.from_user.id not in [duel['challenger']['id'], duel['opponent']['id']]:
        return
    
    # Отправляем ответ
    result = duel_game.submit_answer(message.chat.id, message.from_user.id, message.text)
    
    if result and result != 'waiting':
        handle_round_result(message.chat.id, result)

def handle_round_result(chat_id, result):
    """Обработать результат раунда"""
    if result.startswith('round_finished:'):
        winner = result.split(':')[1]
        duel = duel_game.get_duel(chat_id)
        
        if winner == 'challenger':
            winner_name = duel['challenger']['name']
        elif winner == 'opponent':
            winner_name = duel['opponent']['name']
        else:
            winner_name = "Никто"
        
        score_text = f"📊 Счет: {duel['challenger']['name']} {duel['challenger']['score']} - {duel['opponent']['score']} {duel['opponent']['name']}"
        
        if winner_name != "Никто":
            result_text = f"🏆 Раунд выиграл: {winner_name}!\n{score_text}"
        else:
            result_text = f"🤝 Ничья в раунде!\n{score_text}"
        
        bot.send_message(chat_id, result_text)
        
        # Запускаем следующий раунд через 3 секунды
        def next_round():
            time.sleep(3)
            start_round(chat_id)
        
        import threading
        threading.Thread(target=next_round).start()
        
    elif result.startswith('duel_finished:'):
        parts = result.split(':')
        winner_id = int(parts[1])
        loser_id = int(parts[2])
        
        duel = duel_game.get_duel(chat_id)
        winner_name = duel['winner']['name']
        loser_name = duel['loser']['name']
        
        # Записываем результат в базу данных
        duration = int(time.time() - duel['start_time'])
        db.record_duel(
            chat_id, duel['challenger']['id'], duel['opponent']['id'],
            winner_id, loser_id, duel['type'], duel['round'],
            duel['challenger']['score'], duel['opponent']['score'], duration
        )
        
        final_text = f"🎉 Дуэль завершена!\n\n"
        final_text += f"🏆 Победитель: {winner_name}\n"
        final_text += f"💀 Проигравший: {loser_name}\n\n"
        final_text += f"📊 Финальный счет: {duel['challenger']['score']} - {duel['opponent']['score']}\n"
        final_text += f"⏱ Продолжительность: {duration} сек."
        
        bot.send_message(chat_id, final_text)
        
        # Пытаемся исключить проигравшего
        try:
            bot.kick_chat_member(chat_id, loser_id)
            bot.send_message(chat_id, f"💀 {loser_name} исключен из группы за поражение в дуэли!")
        except Exception as e:
            bot.send_message(chat_id, MESSAGES['no_admin_rights'])
        
        # Очищаем данные дуэли
        duel_game.cleanup_duel(chat_id)

@bot.message_handler(commands=['stats'])
def show_stats(message):
    """Показать статистику пользователя"""
    user_id = message.from_user.id
    if message.reply_to_message:
        user_id = message.reply_to_message.from_user.id
    
    stats = db.get_user_stats(user_id)
    if not stats:
        bot.reply_to(message, "📊 У этого пользователя пока нет статистики дуэлей.")
        return
    
    name = stats['first_name'] or f"@{stats['username']}" or f"User{stats['user_id']}"
    stats_text = f"📊 Статистика дуэлей для {name}:\n\n"
    stats_text += f"🏆 Побед: {stats['wins']}\n"
    stats_text += f"💀 Поражений: {stats['losses']}\n"
    stats_text += f"🎯 Всего дуэлей: {stats['total_duels']}\n"
    stats_text += f"📈 Процент побед: {stats['win_rate']}%"
    
    bot.reply_to(message, stats_text)

@bot.message_handler(commands=['leaderboard', 'top'])
def show_leaderboard(message):
    """Показать таблицу лидеров"""
    leaderboard = db.get_leaderboard(10)
    if not leaderboard:
        bot.reply_to(message, "📊 Таблица лидеров пуста.")
        return
    
    text = "🏆 Таблица лидеров дуэлей:\n\n"
    for entry in leaderboard:
        name = entry['first_name'] or f"@{entry['username']}" or f"User{entry['user_id']}"
        text += f"{entry['rank']}. {name}\n"
        text += f"   🏆 {entry['wins']} побед ({entry['win_rate']}%)\n\n"
    
    bot.reply_to(message, text)

if __name__ == '__main__':
    print("🤺 Бот-секундант запущен!")
    bot.infinity_polling()
