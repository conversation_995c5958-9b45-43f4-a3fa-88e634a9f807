import os
from dotenv import load_dotenv

load_dotenv()

# Токен бота (получить у @BotFather)
BOT_TOKEN = os.getenv('BOT_TOKEN', 'YOUR_BOT_TOKEN_HERE')

# Настройки дуэлей
DUEL_TIMEOUT = 60  # Время на ответ в секундах
MIN_PARTICIPANTS = 2  # Минимальное количество участников для дуэли
MAX_PARTICIPANTS = 10  # Максимальное количество участников

# Типы дуэлей
DUEL_TYPES = {
    'math': 'Математическая дуэль',
    'quiz': 'Викторина',
    'reaction': 'Дуэль на реакцию',
    'random': 'Случайное испытание'
}

# Сообщения
MESSAGES = {
    'duel_start': '⚔️ Дуэль началась! Секундант готов к бою!',
    'duel_challenge': '{challenger} вызывает {opponent} на дуэль!',
    'duel_accepted': '✅ Дуэль принята! Готовьтесь к бою!',
    'duel_declined': '❌ Дуэль отклонена.',
    'duel_winner': '🏆 Победитель: {winner}!',
    'duel_loser_kicked': '💀 {loser} проиграл дуэль и исключен из группы!',
    'no_admin_rights': '❌ У бота нет прав администратора для исключения участников.',
    'help_text': '''
🤺 Бот-секундант для дуэлей

Команды:
/duel @username - вызвать на дуэль
/accept - принять дуэль
/decline - отклонить дуэль
/stats - статистика дуэлей
/help - помощь

Типы дуэлей:
⚡ Математические задачи
🧠 Викторины
⚡ Дуэли на реакцию
🎲 Случайные испытания
    '''
}
