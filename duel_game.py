import random
import time
import math
from typing import Dict, List, Tuple, Optional

class DuelGame:
    def __init__(self):
        self.active_duels = {}  # chat_id: duel_data
        
    def create_duel(self, chat_id: int, challenger_id: int, challenger_name: str, 
                   opponent_id: int, opponent_name: str) -> Dict:
        """Создать новую дуэль"""
        duel_data = {
            'challenger': {'id': challenger_id, 'name': challenger_name, 'score': 0},
            'opponent': {'id': opponent_id, 'name': opponent_name, 'score': 0},
            'status': 'pending',  # pending, active, finished
            'type': random.choice(['math', 'quiz', 'reaction', 'random']),
            'round': 0,
            'max_rounds': 3,
            'current_question': None,
            'answers': {},
            'start_time': time.time()
        }
        self.active_duels[chat_id] = duel_data
        return duel_data
    
    def accept_duel(self, chat_id: int) -> bool:
        """Принять дуэль"""
        if chat_id in self.active_duels:
            self.active_duels[chat_id]['status'] = 'active'
            return True
        return False
    
    def decline_duel(self, chat_id: int) -> bool:
        """Отклонить дуэль"""
        if chat_id in self.active_duels:
            del self.active_duels[chat_id]
            return True
        return False
    
    def get_duel(self, chat_id: int) -> Optional[Dict]:
        """Получить данные дуэли"""
        return self.active_duels.get(chat_id)
    
    def generate_math_question(self) -> Tuple[str, int]:
        """Генерировать математический вопрос"""
        operations = ['+', '-', '*']
        operation = random.choice(operations)
        
        if operation == '+':
            a, b = random.randint(10, 100), random.randint(10, 100)
            answer = a + b
            question = f"{a} + {b} = ?"
        elif operation == '-':
            a, b = random.randint(50, 200), random.randint(10, 49)
            answer = a - b
            question = f"{a} - {b} = ?"
        else:  # multiplication
            a, b = random.randint(2, 15), random.randint(2, 15)
            answer = a * b
            question = f"{a} × {b} = ?"
            
        return question, answer
    
    def generate_quiz_question(self) -> Tuple[str, str]:
        """Генерировать вопрос викторины"""
        questions = [
            ("Столица России?", "москва"),
            ("Сколько дней в году?", "365"),
            ("Автор 'Войны и мира'?", "толстой"),
            ("Самая большая планета?", "юпитер"),
            ("Сколько континентов на Земле?", "7"),
            ("В каком году началась ВОВ?", "1941"),
            ("Химический символ золота?", "au"),
            ("Сколько струн у гитары?", "6"),
            ("Самый большой океан?", "тихий"),
            ("Столица Франции?", "париж")
        ]
        question, answer = random.choice(questions)
        return question, answer.lower()
    
    def generate_reaction_challenge(self) -> str:
        """Генерировать испытание на реакцию"""
        challenges = [
            "Напишите слово 'БЫСТРО' как можно скорее!",
            "Отправьте любой эмодзи первым!",
            "Напишите цифру 7!",
            "Отправьте '+' первым!",
            "Напишите 'ДА' быстрее соперника!"
        ]
        return random.choice(challenges)
    
    def generate_random_challenge(self) -> Tuple[str, str]:
        """Генерировать случайное испытание"""
        challenges = [
            ("Орёл или решка? (напишите 'орел' или 'решка')", random.choice(['орел', 'решка'])),
            ("Угадайте число от 1 до 10", str(random.randint(1, 10))),
            ("Красный или синий? (напишите цвет)", random.choice(['красный', 'синий'])),
            ("Чётное или нечётное число 42?", "четное"),
            ("Сколько букв в слове 'дуэль'?", "5")
        ]
        question, answer = random.choice(challenges)
        return question, answer.lower()
    
    def start_round(self, chat_id: int) -> Optional[Dict]:
        """Начать новый раунд дуэли"""
        duel = self.active_duels.get(chat_id)
        if not duel or duel['status'] != 'active':
            return None
            
        duel['round'] += 1
        duel['answers'] = {}
        
        if duel['type'] == 'math':
            question, answer = self.generate_math_question()
            duel['current_question'] = {'text': question, 'answer': str(answer), 'type': 'exact'}
        elif duel['type'] == 'quiz':
            question, answer = self.generate_quiz_question()
            duel['current_question'] = {'text': question, 'answer': answer, 'type': 'text'}
        elif duel['type'] == 'reaction':
            challenge = self.generate_reaction_challenge()
            duel['current_question'] = {'text': challenge, 'answer': None, 'type': 'reaction'}
        else:  # random
            question, answer = self.generate_random_challenge()
            duel['current_question'] = {'text': question, 'answer': answer, 'type': 'text'}
            
        return duel
    
    def submit_answer(self, chat_id: int, user_id: int, answer: str) -> Optional[str]:
        """Отправить ответ на вопрос"""
        duel = self.active_duels.get(chat_id)
        if not duel or duel['status'] != 'active':
            return None
            
        # Проверяем, участвует ли пользователь в дуэли
        if user_id not in [duel['challenger']['id'], duel['opponent']['id']]:
            return None
            
        # Сохраняем ответ
        duel['answers'][user_id] = {
            'answer': answer.lower().strip(),
            'time': time.time()
        }
        
        # Проверяем, ответили ли оба участника
        if len(duel['answers']) == 2:
            return self.evaluate_round(chat_id)
            
        return 'waiting'
    
    def evaluate_round(self, chat_id: int) -> str:
        """Оценить результаты раунда"""
        duel = self.active_duels.get(chat_id)
        if not duel:
            return 'error'
            
        question = duel['current_question']
        challenger_id = duel['challenger']['id']
        opponent_id = duel['opponent']['id']
        
        challenger_answer = duel['answers'].get(challenger_id, {})
        opponent_answer = duel['answers'].get(opponent_id, {})
        
        winner = None
        
        if question['type'] == 'reaction':
            # Для реакции побеждает тот, кто ответил первым
            if challenger_answer.get('time', float('inf')) < opponent_answer.get('time', float('inf')):
                winner = 'challenger'
            else:
                winner = 'opponent'
        else:
            # Для других типов проверяем правильность ответа
            challenger_correct = challenger_answer.get('answer') == question['answer']
            opponent_correct = opponent_answer.get('answer') == question['answer']
            
            if challenger_correct and not opponent_correct:
                winner = 'challenger'
            elif opponent_correct and not challenger_correct:
                winner = 'opponent'
            elif challenger_correct and opponent_correct:
                # Оба правильно - побеждает тот, кто быстрее
                if challenger_answer.get('time', float('inf')) < opponent_answer.get('time', float('inf')):
                    winner = 'challenger'
                else:
                    winner = 'opponent'
            # Если оба неправильно - никто не получает очко
        
        # Начисляем очки
        if winner == 'challenger':
            duel['challenger']['score'] += 1
        elif winner == 'opponent':
            duel['opponent']['score'] += 1
            
        # Проверяем, закончилась ли дуэль
        if duel['round'] >= duel['max_rounds']:
            return self.finish_duel(chat_id)
        
        return f"round_finished:{winner}"
    
    def finish_duel(self, chat_id: int) -> str:
        """Завершить дуэль и определить победителя"""
        duel = self.active_duels.get(chat_id)
        if not duel:
            return 'error'
            
        challenger_score = duel['challenger']['score']
        opponent_score = duel['opponent']['score']
        
        if challenger_score > opponent_score:
            winner = duel['challenger']
            loser = duel['opponent']
        elif opponent_score > challenger_score:
            winner = duel['opponent']
            loser = duel['challenger']
        else:
            # Ничья - случайный победитель
            if random.choice([True, False]):
                winner = duel['challenger']
                loser = duel['opponent']
            else:
                winner = duel['opponent']
                loser = duel['challenger']
        
        duel['status'] = 'finished'
        duel['winner'] = winner
        duel['loser'] = loser
        
        return f"duel_finished:{winner['id']}:{loser['id']}"
    
    def cleanup_duel(self, chat_id: int):
        """Очистить данные дуэли"""
        if chat_id in self.active_duels:
            del self.active_duels[chat_id]
