import sqlite3
import os
from typing import Dict, List, Optional
from datetime import datetime

class DuelDatabase:
    def __init__(self, db_path: str = "duels.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Инициализировать базу данных"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Таблица пользователей
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    user_id INTEGER PRIMARY KEY,
                    username TEXT,
                    first_name TEXT,
                    last_name TEXT,
                    wins INTEGER DEFAULT 0,
                    losses INTEGER DEFAULT 0,
                    total_duels INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Таблица дуэлей
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS duels (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id INTEGER,
                    challenger_id INTEGER,
                    opponent_id INTEGER,
                    winner_id INTEGER,
                    loser_id INTEGER,
                    duel_type TEXT,
                    rounds INTEGER,
                    challenger_score INTEGER,
                    opponent_score INTEGER,
                    duration INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (challenger_id) REFERENCES users (user_id),
                    FOREIGN KEY (opponent_id) REFERENCES users (user_id),
                    FOREIGN KEY (winner_id) REFERENCES users (user_id),
                    FOREIGN KEY (loser_id) REFERENCES users (user_id)
                )
            ''')
            
            conn.commit()
    
    def add_user(self, user_id: int, username: str = None, first_name: str = None, last_name: str = None):
        """Добавить пользователя в базу данных"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO users (user_id, username, first_name, last_name, wins, losses, total_duels)
                VALUES (?, ?, ?, ?, 
                    COALESCE((SELECT wins FROM users WHERE user_id = ?), 0),
                    COALESCE((SELECT losses FROM users WHERE user_id = ?), 0),
                    COALESCE((SELECT total_duels FROM users WHERE user_id = ?), 0))
            ''', (user_id, username, first_name, last_name, user_id, user_id, user_id))
            conn.commit()
    
    def get_user_stats(self, user_id: int) -> Optional[Dict]:
        """Получить статистику пользователя"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT user_id, username, first_name, wins, losses, total_duels
                FROM users WHERE user_id = ?
            ''', (user_id,))
            
            row = cursor.fetchone()
            if row:
                return {
                    'user_id': row[0],
                    'username': row[1],
                    'first_name': row[2],
                    'wins': row[3],
                    'losses': row[4],
                    'total_duels': row[5],
                    'win_rate': round((row[3] / max(row[5], 1)) * 100, 1)
                }
            return None
    
    def record_duel(self, chat_id: int, challenger_id: int, opponent_id: int, 
                   winner_id: int, loser_id: int, duel_type: str, 
                   rounds: int, challenger_score: int, opponent_score: int, duration: int):
        """Записать результат дуэли"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Записываем дуэль
            cursor.execute('''
                INSERT INTO duels (chat_id, challenger_id, opponent_id, winner_id, loser_id,
                                 duel_type, rounds, challenger_score, opponent_score, duration)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (chat_id, challenger_id, opponent_id, winner_id, loser_id,
                  duel_type, rounds, challenger_score, opponent_score, duration))
            
            # Обновляем статистику победителя
            cursor.execute('''
                UPDATE users SET wins = wins + 1, total_duels = total_duels + 1
                WHERE user_id = ?
            ''', (winner_id,))
            
            # Обновляем статистику проигравшего
            cursor.execute('''
                UPDATE users SET losses = losses + 1, total_duels = total_duels + 1
                WHERE user_id = ?
            ''', (loser_id,))
            
            conn.commit()
    
    def get_leaderboard(self, limit: int = 10) -> List[Dict]:
        """Получить таблицу лидеров"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT user_id, username, first_name, wins, losses, total_duels,
                       ROUND((CAST(wins AS FLOAT) / MAX(total_duels, 1)) * 100, 1) as win_rate
                FROM users 
                WHERE total_duels > 0
                ORDER BY wins DESC, win_rate DESC
                LIMIT ?
            ''', (limit,))
            
            rows = cursor.fetchall()
            leaderboard = []
            for i, row in enumerate(rows, 1):
                leaderboard.append({
                    'rank': i,
                    'user_id': row[0],
                    'username': row[1],
                    'first_name': row[2],
                    'wins': row[3],
                    'losses': row[4],
                    'total_duels': row[5],
                    'win_rate': row[6]
                })
            
            return leaderboard
    
    def get_recent_duels(self, chat_id: int = None, limit: int = 10) -> List[Dict]:
        """Получить последние дуэли"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            if chat_id:
                cursor.execute('''
                    SELECT d.*, 
                           c.username as challenger_username, c.first_name as challenger_name,
                           o.username as opponent_username, o.first_name as opponent_name,
                           w.username as winner_username, w.first_name as winner_name
                    FROM duels d
                    LEFT JOIN users c ON d.challenger_id = c.user_id
                    LEFT JOIN users o ON d.opponent_id = o.user_id
                    LEFT JOIN users w ON d.winner_id = w.user_id
                    WHERE d.chat_id = ?
                    ORDER BY d.created_at DESC
                    LIMIT ?
                ''', (chat_id, limit))
            else:
                cursor.execute('''
                    SELECT d.*, 
                           c.username as challenger_username, c.first_name as challenger_name,
                           o.username as opponent_username, o.first_name as opponent_name,
                           w.username as winner_username, w.first_name as winner_name
                    FROM duels d
                    LEFT JOIN users c ON d.challenger_id = c.user_id
                    LEFT JOIN users o ON d.opponent_id = o.user_id
                    LEFT JOIN users w ON d.winner_id = w.user_id
                    ORDER BY d.created_at DESC
                    LIMIT ?
                ''', (limit,))
            
            rows = cursor.fetchall()
            duels = []
            for row in rows:
                duels.append({
                    'id': row[0],
                    'chat_id': row[1],
                    'challenger_id': row[2],
                    'opponent_id': row[3],
                    'winner_id': row[4],
                    'loser_id': row[5],
                    'duel_type': row[6],
                    'rounds': row[7],
                    'challenger_score': row[8],
                    'opponent_score': row[9],
                    'duration': row[10],
                    'created_at': row[11],
                    'challenger_name': row[13] or row[12],
                    'opponent_name': row[15] or row[14],
                    'winner_name': row[17] or row[16]
                })
            
            return duels
