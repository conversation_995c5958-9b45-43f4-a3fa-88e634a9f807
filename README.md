# 🤺 Telegram Бот-Секундант для Дуэлей

Telegram бот, который организует дуэли между участниками группы. Проигравший исключается из группы!

## 🎯 Возможности

- **Различные типы дуэлей**: математические задачи, викторины, дуэли на реакцию, случайные испытания
- **Система раундов**: каждая дуэль состоит из 3 раундов
- **Автоматическое исключение**: проигравший автоматически исключается из группы
- **Статистика**: ведется учет побед и поражений каждого участника
- **Таблица лидеров**: рейтинг лучших дуэлянтов

## 🚀 Установка и запуск

### 1. Создание бота

1. Найдите [@BotFather](https://t.me/BotFather) в Telegram
2. Отправьте команду `/newbot`
3. Следуйте инструкциям для создания бота
4. Скопируйте полученный токен

### 2. Настройка проекта

```bash
# Установите зависимости
pip install -r requirements.txt

# Скопируйте и отредактируйте файл конфигурации
cp .env.example .env
```

### 3. Конфигурация

Отредактируйте файл `.env`:
```
BOT_TOKEN=ваш_токен_от_BotFather
```

### 4. Запуск бота

```bash
python duel_bot.py
```

## 🎮 Команды бота

### Основные команды
- `/duel @username` - вызвать пользователя на дуэль (или ответить на сообщение)
- `/accept` - принять дуэль (через inline-кнопку)
- `/decline` - отклонить дуэль (через inline-кнопку)
- `/stats` - показать свою статистику
- `/stats` (в ответ на сообщение) - показать статистику другого пользователя
- `/leaderboard` или `/top` - таблица лидеров
- `/help` - справка по командам

### Права администратора

⚠️ **Важно**: Для исключения проигравших участников бот должен быть администратором группы с правом исключения пользователей.

## 🎯 Типы дуэлей

### 1. Математические дуэли 🧮
- Решение арифметических примеров
- Сложение, вычитание, умножение
- Побеждает тот, кто быстрее даст правильный ответ

### 2. Викторины 🧠
- Вопросы на общие знания
- География, история, наука
- Правильность ответа важнее скорости

### 3. Дуэли на реакцию ⚡
- Задания на скорость
- Первый правильный ответ побеждает
- Проверка внимательности и реакции

### 4. Случайные испытания 🎲
- Угадывание чисел
- Выбор из вариантов
- Элемент удачи

## 📊 Система подсчета очков

- Каждая дуэль состоит из **3 раундов**
- За победу в раунде начисляется **1 очко**
- Побеждает участник с большим количеством очков
- При ничьей победитель определяется случайно

## 🛡️ Безопасность

- Нельзя вызвать самого себя на дуэль
- Нельзя вызвать ботов на дуэль
- Одновременно может проходить только одна дуэль в группе
- Автоматическая отмена дуэли через 60 секунд без ответа

## 📁 Структура проекта

```
telegram-duel-bot/
├── duel_bot.py          # Основной файл бота
├── duel_game.py         # Логика дуэлей
├── database.py          # Работа с базой данных
├── config.py            # Конфигурация
├── requirements.txt     # Зависимости
├── .env                 # Переменные окружения
├── duels.db            # База данных SQLite (создается автоматически)
└── README.md           # Документация
```

## 🔧 Настройка

В файле `config.py` можно изменить:
- Время на ответ в дуэли (`DUEL_TIMEOUT`)
- Количество раундов в дуэли
- Типы дуэлей
- Тексты сообщений

## 🐛 Устранение неполадок

### Бот не исключает проигравших
- Убедитесь, что бот является администратором группы
- Проверьте, что у бота есть право исключения пользователей

### Бот не отвечает на команды
- Проверьте правильность токена в файле `.env`
- Убедитесь, что бот запущен
- Проверьте логи на наличие ошибок

### База данных не создается
- Убедитесь, что у бота есть права на запись в директорию
- Проверьте, установлен ли модуль `sqlite3`

## 📝 Лицензия

Этот проект распространяется под лицензией MIT.

## 🤝 Вклад в проект

Приветствуются предложения по улучшению! Создавайте Issues и Pull Requests.

---

**Удачных дуэлей! ⚔️**
